import type { Metadata } from "next"
import { requireAuth } from "@/lib/auth"
import { Navigation } from "@/components/navigation"
import { Footer } from "@/components/footer"
import { WebsiteCard } from "./website-card"

export const metadata: Metadata = {
  title: "Dashboard - St Cloud Enterprises Portal",
  description: "Manage your St Cloud Enterprises website properties",
}

// Mock website data - in a real app, this would come from a database
const websites = [
  {
    id: "1",
    name: "St Cloud Marketing Solutions",
    description:
      "Comprehensive digital marketing services including SEO, social media management, and content creation for businesses of all sizes.",
    image: "/placeholder-nmdq7.png",
    establishedDate: "2019-03-15",
    url: "https://marketing.stcloud.com",
  },
  {
    id: "2",
    name: "St Cloud E-Commerce Platform",
    description:
      "Full-featured online shopping platform with inventory management, payment processing, and customer analytics.",
    image: "/ecommerce-website.png",
    establishedDate: "2020-07-22",
    url: "https://shop.stcloud.com",
  },
  {
    id: "3",
    name: "St Cloud Real Estate Portal",
    description:
      "Property listing and management system for real estate professionals with virtual tours and client management tools.",
    image: "/real-estate-website.png",
    establishedDate: "2018-11-08",
    url: "https://realestate.stcloud.com",
  },
  {
    id: "4",
    name: "St Cloud Healthcare Network",
    description:
      "Patient portal and appointment scheduling system for healthcare providers with secure messaging and medical records.",
    image: "/healthcare-website.png",
    establishedDate: "2021-01-30",
    url: "https://health.stcloud.com",
  },
  {
    id: "5",
    name: "St Cloud Education Hub",
    description:
      "Learning management system for educational institutions with course materials, assignments, and student progress tracking.",
    image: "/education-website.png",
    establishedDate: "2020-09-12",
    url: "https://edu.stcloud.com",
  },
  {
    id: "6",
    name: "St Cloud Financial Services",
    description:
      "Secure banking and financial management platform with account management, transactions, and investment tracking.",
    image: "/financial-banking-interface.png",
    establishedDate: "2017-05-03",
    url: "https://finance.stcloud.com",
  },
]

export default async function DashboardPage() {
  // Ensure user is authenticated
  await requireAuth()

  return (
    <div className="min-h-screen flex flex-col">
      <Navigation />

      <main className="flex-1 py-8">
        <div className="container mx-auto px-4 max-w-6xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">Dashboard</h1>
            <p className="text-muted-foreground">Manage your St Cloud Enterprises website properties</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {websites.map((website) => (
              <WebsiteCard key={website.id} website={website} />
            ))}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
