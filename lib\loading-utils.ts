"use client"

import { useEffect, useState } from "react"

export function useActionLoading(delay = 500) {
  const [showLoading, setShowLoading] = useState(false)

  const startLoading = () => {
    const timer = setTimeout(() => {
      setShowLoading(true)
    }, delay)

    return () => {
      clearTimeout(timer)
      setShowLoading(false)
    }
  }

  return { showLoading, startLoading }
}

export function usePageLoading(delay = 1000) {
  const [showLoading, setShowLoading] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowLoading(true)
    }, delay)

    return () => clearTimeout(timer)
  }, [delay])

  return showLoading
}
