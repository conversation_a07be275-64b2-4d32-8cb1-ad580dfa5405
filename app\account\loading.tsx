import { Navigation } from "@/components/navigation"
import { Footer } from "@/components/footer"

export default function AccountLoading() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navigation />

      <main className="flex-1 py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="mb-8">
            <div className="h-8 bg-muted rounded w-64 mb-2 animate-pulse" />
            <div className="h-4 bg-muted rounded w-96 animate-pulse" />
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            <div className="md:col-span-2">
              <div className="bg-card border rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-muted rounded w-48 mb-6" />
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded w-20" />
                      <div className="h-10 bg-muted rounded" />
                    </div>
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded w-24" />
                      <div className="h-10 bg-muted rounded" />
                    </div>
                  </div>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded w-28" />
                      <div className="h-10 bg-muted rounded" />
                    </div>
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded w-20" />
                      <div className="h-10 bg-muted rounded" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded w-12" />
                    <div className="h-24 bg-muted rounded" />
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-card border rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-muted rounded w-32 mb-4" />
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex justify-between items-center">
                      <div className="h-4 bg-muted rounded w-20" />
                      <div className="h-4 bg-muted rounded w-16" />
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-card border rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-muted rounded w-28 mb-4" />
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="h-8 bg-muted rounded" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
