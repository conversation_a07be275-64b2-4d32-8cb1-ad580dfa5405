import Image from "next/image"
import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"

interface Website {
  id: string
  name: string
  description: string
  image: string
  establishedDate: string
  url: string
}

interface WebsiteCardProps {
  website: Website
}

export function WebsiteCard({ website }: WebsiteCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <Card className="h-full flex flex-col hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-foreground line-clamp-2">{website.name}</CardTitle>
      </CardHeader>

      <CardContent className="flex-1 space-y-4">
        <div className="relative aspect-video w-full overflow-hidden rounded-md">
          <Image
            src={website.image || "/placeholder.svg"}
            alt={`Screenshot of ${website.name}`}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>

        <p className="text-sm text-muted-foreground line-clamp-3">{website.description}</p>

        <Button asChild variant="outline" size="sm" className="w-full bg-transparent">
          <Link href={website.url} target="_blank" rel="noopener noreferrer">
            Visit Website
            <ExternalLink className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardContent>

      <CardFooter className="pt-4">
        <div className="w-full text-right">
          <p className="text-xs text-muted-foreground">Established: {formatDate(website.establishedDate)}</p>
        </div>
      </CardFooter>
    </Card>
  )
}
