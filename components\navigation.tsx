import Link from "next/link"
import { getCurrentUser } from "@/lib/auth"
import { SignOutButton } from "./sign-out-button"

export async function Navigation() {
  const user = await getCurrentUser()

  if (!user) {
    return null
  }

  return (
    <nav className="border-b bg-background">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="flex items-center justify-between h-16">
          <Link href="/dashboard" className="text-lg font-semibold text-foreground">
            St Cloud Enterprises Portal
          </Link>
          <div className="flex items-center space-x-6">
            <Link href="/dashboard" className="text-muted-foreground hover:text-foreground transition-colors">
              Dashboard
            </Link>
            <Link href="/account" className="text-muted-foreground hover:text-foreground transition-colors">
              Account
            </Link>
            <SignOutButton />
          </div>
        </div>
      </div>
    </nav>
  )
}
