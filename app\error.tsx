"use client"

import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Footer } from "@/components/footer"

export default function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Application error:", error)
  }, [error])

  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="text-center space-y-6 max-w-md">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold text-foreground">500</h1>
            <h2 className="text-2xl font-semibold text-foreground">Something went wrong</h2>
            <p className="text-muted-foreground">
              An unexpected error occurred while processing your request. Please try again or contact support if the
              problem persists.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button onClick={reset}>Try Again</Button>
            <Button variant="outline" onClick={() => (window.location.href = "/")}>
              Go Home
            </Button>
          </div>
          {error.digest && <p className="text-xs text-muted-foreground mt-4">Error ID: {error.digest}</p>}
        </div>
      </main>
      <Footer />
    </div>
  )
}
