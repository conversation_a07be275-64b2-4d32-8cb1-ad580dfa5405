import type { Metada<PERSON> } from "next"
import { requireAuth } from "@/lib/auth"
import { Navigation } from "@/components/navigation"
import { Footer } from "@/components/footer"
import { AccountForm } from "./account-form"

export const metadata: Metadata = {
  title: "Account - St Cloud Enterprises Portal",
  description: "Manage your account settings and profile information",
}

export default async function AccountPage() {
  // Ensure user is authenticated and get user data
  const user = await requireAuth()

  return (
    <div className="min-h-screen flex flex-col">
      <Navigation />

      <main className="flex-1 py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">Account Settings</h1>
            <p className="text-muted-foreground">Manage your profile information and account preferences</p>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            <div className="md:col-span-2">
              <AccountForm user={user} />
            </div>

            <div className="space-y-6">
              <div className="bg-card border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Account Status</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Status</span>
                    <span className="text-sm font-medium text-green-600">Active</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Member Since</span>
                    <span className="text-sm font-medium">March 2019</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Last Login</span>
                    <span className="text-sm font-medium">Today</span>
                  </div>
                </div>
              </div>

              <div className="bg-card border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Quick Actions</h3>
                <div className="space-y-2">
                  <button className="w-full text-left text-sm text-muted-foreground hover:text-foreground transition-colors py-2">
                    Change Password
                  </button>
                  <button className="w-full text-left text-sm text-muted-foreground hover:text-foreground transition-colors py-2">
                    Download Account Data
                  </button>
                  <button className="w-full text-left text-sm text-muted-foreground hover:text-foreground transition-colors py-2">
                    Security Settings
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
