"use client"

import type { ReactNode } from "react"

interface MDXContentProps {
  children: ReactNode
}

export function MDXContent({ children }: MDXContentProps) {
  return (
    <div className="prose prose-zinc max-w-none dark:prose-invert">
      <style jsx global>{`
        .prose {
          color: hsl(var(--foreground));
        }
        .prose h1 {
          color: hsl(var(--foreground));
          font-weight: 700;
          font-size: 2.25rem;
          line-height: 2.5rem;
          margin-bottom: 2rem;
        }
        .prose h2 {
          color: hsl(var(--foreground));
          font-weight: 600;
          font-size: 1.875rem;
          line-height: 2.25rem;
          margin-top: 2rem;
          margin-bottom: 1rem;
        }
        .prose h3 {
          color: hsl(var(--foreground));
          font-weight: 600;
          font-size: 1.5rem;
          line-height: 2rem;
          margin-top: 1.5rem;
          margin-bottom: 0.75rem;
        }
        .prose p {
          color: hsl(var(--muted-foreground));
          margin-bottom: 1rem;
          line-height: 1.75;
        }
        .prose ul,
        .prose ol {
          color: hsl(var(--muted-foreground));
          margin-bottom: 1rem;
        }
        .prose li {
          margin-bottom: 0.5rem;
        }
        .prose strong {
          color: hsl(var(--foreground));
          font-weight: 600;
        }
        .prose a {
          color: hsl(var(--primary));
          text-decoration: underline;
        }
        .prose a:hover {
          color: hsl(var(--primary));
          opacity: 0.8;
        }
        .prose blockquote {
          border-left: 4px solid hsl(var(--border));
          padding-left: 1rem;
          margin: 1.5rem 0;
          font-style: italic;
          color: hsl(var(--muted-foreground));
        }
        .prose code {
          background-color: hsl(var(--muted));
          color: hsl(var(--foreground));
          padding: 0.125rem 0.25rem;
          border-radius: 0.25rem;
          font-size: 0.875rem;
        }
        .prose pre {
          background-color: hsl(var(--muted));
          color: hsl(var(--foreground));
          padding: 1rem;
          border-radius: 0.5rem;
          overflow-x: auto;
          margin: 1rem 0;
        }
      `}</style>
      {children}
    </div>
  )
}
