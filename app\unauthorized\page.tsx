import type { <PERSON><PERSON><PERSON> } from "next"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Footer } from "@/components/footer"

export const metadata: Metadata = {
  title: "Unauthorized - St Cloud Enterprises Portal",
  description: "You are not authorized to access this page",
}

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="text-center space-y-6 max-w-md">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold text-foreground">401</h1>
            <h2 className="text-2xl font-semibold text-foreground">Unauthorized Access</h2>
            <p className="text-muted-foreground">
              You don't have permission to access this page. Please sign in with valid credentials.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link href="/sign-in">Sign In</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/">Go Home</Link>
            </Button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
