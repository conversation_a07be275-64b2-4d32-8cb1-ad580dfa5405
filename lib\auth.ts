import { cookies } from "next/headers"
import { redirect } from "next/navigation"

export interface User {
  id: string
  email: string
  name: string
}

// Mock user data - in a real app, this would come from a database
const mockUsers: User[] = [
  { id: "1", email: "<EMAIL>", name: "Admin User" },
  { id: "2", email: "<EMAIL>", name: "Regular User" },
]

export async function signIn(email: string, password: string): Promise<{ success: boolean; error?: string }> {
  // Mock authentication - in a real app, verify against database
  const user = mockUsers.find((u) => u.email === email)

  if (!user || password !== "password123") {
    return { success: false, error: "Invalid email or password" }
  }

  // Set authentication cookie
  const cookieStore = await cookies()
  cookieStore.set("auth-token", user.id, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 60 * 60 * 24 * 7, // 7 days
  })

  return { success: true }
}

export async function signOut() {
  const cookieStore = await cookies()
  cookieStore.delete("auth-token")
  redirect("/")
}

export async function getCurrentUser(): Promise<User | null> {
  const cookieStore = await cookies()
  const token = cookieStore.get("auth-token")

  if (!token) {
    return null
  }

  // In a real app, verify the token and get user from database
  const user = mockUsers.find((u) => u.id === token.value)
  return user || null
}

export async function requireAuth(): Promise<User> {
  const user = await getCurrentUser()

  if (!user) {
    redirect("/sign-in")
  }

  return user
}
