import type { <PERSON>ada<PERSON> } from "next"
import { SignInForm } from "./sign-in-form"
import { Footer } from "@/components/footer"

export const metadata: Metadata = {
  title: "Sign In - St Cloud Enterprises Portal",
  description: "Sign in to access your St Cloud Enterprises Portal",
}

export default function SignInPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-foreground">Sign In</h1>
            <p className="mt-2 text-muted-foreground">Access your St Cloud Enterprises Portal</p>
          </div>
          <SignInForm />
        </div>
      </main>
      <Footer />
    </div>
  )
}
